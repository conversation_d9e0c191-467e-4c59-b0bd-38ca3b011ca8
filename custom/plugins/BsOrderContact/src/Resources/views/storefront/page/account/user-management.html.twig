{% sw_extends '@Storefront/storefront/page/account/index.html.twig' %}

{% block page_account_main_content %}
    <div class="account-user-management"
         data-bs-user-management-plugin="true"
         data-bs-user-management-plugin-options='{
             "addContactUrl": "{{ path('frontend.account.user.management.add.contact') }}",
             "removeContactUrl": "{{ path('frontend.account.user.management.remove.contact') }}",
             "translations": {
                 "pleaseEnterContactPerson": "{{ "bs.orderContact.userManagement.pleaseEnterContactPerson"|trans|sw_sanitize }}",
                 "errorAddingContact": "{{ "bs.orderContact.userManagement.errorAddingContact"|trans|sw_sanitize }}",
                 "errorRemovingContact": "{{ "bs.orderContact.userManagement.errorRemovingContact"|trans|sw_sanitize }}",
                 "confirmRemoveContact": "{{ "bs.orderContact.userManagement.confirmRemoveContact"|trans|sw_sanitize }}"
             }
         }'>

        <!-- Hidden CSRF tokens for AJAX operations -->
        <div style="display: none;">
            {{ sw_csrf('frontend.account.user.management.add.contact', {'id': 'csrf-add-contact'}) }}
            {{ sw_csrf('frontend.account.user.management.remove.contact', {'id': 'csrf-remove-contact'}) }}
        </div>
        <div class="account-welcome">
            <h1>{{ "bs.orderContact.userManagement.title"|trans|sw_sanitize }}</h1>
            <p>{{ "bs.orderContact.userManagement.description"|trans|sw_sanitize }}</p>
        </div>

        {% if customers|length > 0 %}
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">{{ "bs.orderContact.userManagement.emailAddresses"|trans|sw_sanitize }}</h5>
                    
                    {% for customer in customers %}
                        <div class="user-management-email-section mb-4 p-3 border rounded">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="mb-2">
                                        <strong>{{ customer.email }}</strong>
                                        {% if customer.firstName and customer.lastName %}
                                            <small class="text-muted">({{ customer.firstName }} {{ customer.lastName }})</small>
                                        {% endif %}
                                    </h6>
                                </div>
                                <div class="col-md-6 text-md-right">
                                    <button type="button"
                                            class="btn btn-sm btn-primary add-contact-btn"
                                            data-customer-id="{{ customer.id }}"
                                            data-email="{{ customer.email }}"
                                            data-bs-toggle="modal"
                                            data-bs-target="#addContactModal">
                                        {{ "bs.orderContact.userManagement.addContact"|trans|sw_sanitize }}
                                    </button>
                                </div>
                            </div>

                            <div class="contact-persons-list mt-3">
                                <h6>{{ "bs.orderContact.userManagement.contactPersons"|trans|sw_sanitize }}:</h6>
                                {% if customer.contactPersons|length > 0 %}
                                    <div class="row">
                                        {% for contact in customer.contactPersons %}
                                            <div class="col-md-6 mb-2">
                                                <div class="contact-person-item d-flex justify-content-between align-items-center p-2 bg-light rounded">
                                                    <span>{{ contact.contactPerson }}</span>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-outline-danger remove-contact-btn" 
                                                            data-contact-id="{{ contact.id }}"
                                                            title="{{ "bs.orderContact.userManagement.removeContact"|trans|sw_sanitize }}">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <p class="text-muted">{{ "bs.orderContact.userManagement.noContactPersons"|trans|sw_sanitize }}</p>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% else %}
            <div class="alert alert-info">
                {{ "bs.orderContact.userManagement.noEmailAddresses"|trans|sw_sanitize }}
            </div>
        {% endif %}
    </div>

    <!-- Add Contact Modal -->
    <div class="modal fade" id="addContactModal" tabindex="-1" aria-labelledby="addContactModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addContactModalLabel">{{ "bs.orderContact.userManagement.addContactPerson"|trans|sw_sanitize }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addContactForm">
                        {{ sw_csrf('frontend.account.user.management.add.contact') }}
                        <div class="mb-3">
                            <label for="modalEmail" class="form-label">{{ "bs.orderContact.userManagement.emailAddress"|trans|sw_sanitize }}</label>
                            <input type="email" class="form-control" id="modalEmail" readonly>
                            <input type="hidden" id="modalCustomerId">
                        </div>
                        <div class="mb-3">
                            <label for="contactPersonName" class="form-label">{{ "bs.orderContact.userManagement.contactPersonName"|trans|sw_sanitize }}</label>
                            <input type="text" class="form-control" id="contactPersonName" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ "general.cancel"|trans|sw_sanitize }}</button>
                    <button type="button" class="btn btn-primary" id="saveContactBtn">{{ "general.save"|trans|sw_sanitize }}</button>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block page_account_main_content_inner %}{% endblock %}


